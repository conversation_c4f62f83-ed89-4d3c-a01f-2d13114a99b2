<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏夹API调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 收藏夹API调试工具</h1>
        
        <div class="section">
            <h3>📊 认证状态</h3>
            <div id="auth-status" class="status info">检查中...</div>
            <button class="btn" onclick="checkAuth()">🔄 检查认证</button>
        </div>

        <div class="section">
            <h3>📥 获取收藏列表</h3>
            <div id="favorites-status" class="status info">等待测试...</div>
            <button class="btn" onclick="testGetFavorites()">📋 获取收藏列表</button>
            <button class="btn" onclick="testGetFavoritesLarge()">📋 获取大页面收藏</button>
            <div id="favorites-data"></div>
        </div>

        <div class="section">
            <h3>🔧 API原始调用</h3>
            <button class="btn" onclick="testRawAPI()">🧪 原始API调用</button>
            <button class="btn" onclick="testFrontendIntegration()">🔗 前端集成测试</button>
            <button class="btn danger" onclick="clearLog()">🗑️ 清除日志</button>
            <div id="api-log" class="log">等待测试...</div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>

    <script>
        const API_BASE = 'http://localhost:8086/api';
        let authToken = null;
        let authInfo = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('api-log');
            logElement.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function clearLog() {
            document.getElementById('api-log').textContent = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const statusDiv = document.getElementById(elementId);
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function checkAuth() {
            log('检查认证状态...');
            
            // 检查localStorage中的认证信息
            const authData = localStorage.getItem('fileShareAuth');
            if (authData) {
                try {
                    authInfo = JSON.parse(authData);
                    authToken = authInfo.token;
                    log(`找到认证信息: 用户ID=${authInfo.user_id}, Token=${authToken ? '已设置' : '未设置'}`);
                    updateStatus('auth-status', `✅ 已认证 - 用户ID: ${authInfo.user_id}`, 'success');
                } catch (error) {
                    log(`认证信息解析失败: ${error.message}`, 'error');
                    updateStatus('auth-status', '❌ 认证信息格式错误', 'error');
                }
            } else {
                log('未找到认证信息', 'error');
                updateStatus('auth-status', '❌ 未登录', 'error');
            }
        }

        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            log(`API请求: ${options.method || 'GET'} ${url}`);
            if (options.body) {
                log(`请求体: ${options.body}`);
            }

            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                });

                log(`响应状态: ${response.status} ${response.statusText}`);

                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (response.ok) {
                    return { success: true, data };
                } else {
                    return { success: false, error: data.error || '未知错误', status: response.status };
                }
            } catch (error) {
                log(`请求异常: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        async function testGetFavorites() {
            log('=== 测试获取收藏列表 ===');
            updateStatus('favorites-status', '正在获取收藏列表...', 'info');

            if (!authToken) {
                updateStatus('favorites-status', '❌ 请先登录', 'error');
                return;
            }

            const result = await apiRequest('/favorites');
            
            if (result.success) {
                const data = result.data;
                const count = data.favorites?.length || 0;
                const total = data.total_count || 0;
                
                updateStatus('favorites-status', `✅ 获取成功: ${count}/${total} 个收藏`, 'success');
                
                // 显示收藏数据表格
                displayFavoritesTable(data.favorites || []);
                
            } else {
                updateStatus('favorites-status', `❌ 获取失败: ${result.error}`, 'error');
            }
        }

        async function testGetFavoritesLarge() {
            log('=== 测试获取大页面收藏列表 ===');
            updateStatus('favorites-status', '正在获取大页面收藏列表...', 'info');

            if (!authToken) {
                updateStatus('favorites-status', '❌ 请先登录', 'error');
                return;
            }

            const result = await apiRequest('/favorites?page=1&page_size=1000');
            
            if (result.success) {
                const data = result.data;
                const count = data.favorites?.length || 0;
                const total = data.total_count || 0;
                
                updateStatus('favorites-status', `✅ 大页面获取成功: ${count}/${total} 个收藏`, 'success');
                
                // 显示收藏数据表格
                displayFavoritesTable(data.favorites || []);
                
            } else {
                updateStatus('favorites-status', `❌ 大页面获取失败: ${result.error}`, 'error');
            }
        }

        function displayFavoritesTable(favorites) {
            const container = document.getElementById('favorites-data');
            
            if (favorites.length === 0) {
                container.innerHTML = '<p>没有收藏数据</p>';
                return;
            }

            let tableHTML = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>收藏ID</th>
                            <th>文件ID</th>
                            <th>文件名</th>
                            <th>文件大小</th>
                            <th>收藏时间</th>
                            <th>文件夹</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            favorites.forEach(fav => {
                const file = fav.file || {};
                tableHTML += `
                    <tr>
                        <td>${fav.id}</td>
                        <td>${fav.file_id}</td>
                        <td>${file.filename || '未知'}</td>
                        <td>${file.file_size ? formatFileSize(file.file_size) : '-'}</td>
                        <td>${fav.favorited_at ? new Date(fav.favorited_at).toLocaleString() : '-'}</td>
                        <td>${file.folder_name || '未知文件夹'}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            container.innerHTML = tableHTML;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        async function testRawAPI() {
            log('=== 原始API调用测试 ===');

            if (!authToken) {
                log('请先登录', 'error');
                return;
            }

            // 测试多个API端点
            const endpoints = [
                '/favorites',
                '/favorites?page=1&page_size=10',
                '/favorites?page=1&page_size=1000'
            ];

            for (const endpoint of endpoints) {
                log(`\n--- 测试端点: ${endpoint} ---`);
                const result = await apiRequest(endpoint);

                if (result.success) {
                    const data = result.data;
                    log(`✅ 成功: 获取到 ${data.favorites?.length || 0}/${data.total_count || 0} 个收藏`);

                    // 详细检查数据结构
                    if (data.favorites && data.favorites.length > 0) {
                        const firstFav = data.favorites[0];
                        log(`第一个收藏项结构:`);
                        log(`- ID: ${firstFav.id}`);
                        log(`- 文件ID: ${firstFav.file_id}`);
                        log(`- 收藏时间: ${firstFav.favorited_at}`);
                        log(`- 文件信息: ${firstFav.file ? '存在' : '缺失'}`);
                        if (firstFav.file) {
                            log(`  - 文件名: ${firstFav.file.filename}`);
                            log(`  - 文件大小: ${firstFav.file.file_size}`);
                            log(`  - 是否图片: ${firstFav.file.is_image}`);
                            log(`  - 文件夹名: ${firstFav.file.folder_name}`);
                        }
                    }
                } else {
                    log(`❌ 失败: ${result.error}`, 'error');
                }
            }
        }

        async function testFrontendIntegration() {
            log('=== 前端集成测试 ===');

            if (!authToken) {
                log('请先登录', 'error');
                return;
            }

            try {
                // 模拟前端的FavoriteAPI调用
                log('测试 FavoriteAPI.getFavorites...');

                // 检查FavoriteAPI是否存在
                if (typeof FavoriteAPI === 'undefined') {
                    log('❌ FavoriteAPI 未定义', 'error');
                    return;
                }

                const result = await FavoriteAPI.getFavorites(1, 1000);
                log(`FavoriteAPI 返回结果:`, JSON.stringify(result, null, 2));

                if (result && result.favorites) {
                    log(`✅ FavoriteAPI 成功: ${result.favorites.length} 个收藏`);
                } else if (result && result.data && result.data.favorites) {
                    log(`✅ FavoriteAPI 成功: ${result.data.favorites.length} 个收藏`);
                } else {
                    log(`⚠️ FavoriteAPI 返回格式异常`, 'warning');
                }

            } catch (error) {
                log(`❌ FavoriteAPI 调用失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查认证
        window.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });
    </script>
</body>
</html>
