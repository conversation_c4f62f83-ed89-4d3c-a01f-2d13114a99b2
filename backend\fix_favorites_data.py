#!/usr/bin/env python3
"""
修复收藏数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import Database

def fix_favorites_data():
    """修复收藏数据"""
    db = Database()
    
    try:
        # 连接数据库
        db.connect()
        
        print("=== 修复收藏数据 ===")
        
        # 1. 查找test1用户
        print("\n1. 查找test1用户:")
        test1_query = "SELECT id, username FROM users WHERE username = 'test1'"
        test1_user = db.fetch_one(test1_query)
        
        if not test1_user:
            print("❌ 未找到test1用户")
            return
        
        user_id = test1_user['id']
        print(f"✅ 找到test1用户: ID={user_id}")
        
        # 2. 检查收藏记录的is_active字段
        print(f"\n2. 检查收藏记录的is_active字段:")
        check_query = """
        SELECT 
            id,
            file_id,
            is_active,
            favorited_at
        FROM favorites 
        WHERE user_id = %s
        ORDER BY favorited_at DESC
        """
        
        favorites = db.fetch_all(check_query, (user_id,))
        print(f"总收藏记录数: {len(favorites)}")
        
        inactive_count = 0
        for fav in favorites:
            if not fav['is_active']:
                inactive_count += 1
            print(f"  收藏ID: {fav['id']}, 文件ID: {fav['file_id']}, 活跃: {fav['is_active']}")
        
        print(f"非活跃记录数: {inactive_count}")
        
        # 3. 修复is_active字段（如果需要）
        if inactive_count > 0:
            print(f"\n3. 修复is_active字段:")
            fix_query = """
            UPDATE favorites 
            SET is_active = 1 
            WHERE user_id = %s AND is_active = 0
            """
            
            result = db.execute(fix_query, (user_id,))
            print(f"✅ 修复了 {result} 条记录")
        
        # 4. 检查文件的is_image字段
        print(f"\n4. 检查文件的is_image字段:")
        files_query = """
        SELECT DISTINCT
            f.id,
            f.filename,
            f.is_image
        FROM files f
        JOIN favorites fav ON f.id = fav.file_id
        WHERE fav.user_id = %s
        ORDER BY f.id
        """
        
        files = db.fetch_all(files_query, (user_id,))
        print(f"收藏的文件数: {len(files)}")
        
        non_image_count = 0
        for file in files:
            if not file['is_image']:
                non_image_count += 1
            print(f"  文件ID: {file['id']}, 文件名: {file['filename']}, 是否图片: {file['is_image']}")
        
        print(f"非图片文件数: {non_image_count}")
        
        # 5. 修复is_image字段（根据文件扩展名）
        if non_image_count > 0:
            print(f"\n5. 修复is_image字段:")
            
            # 获取需要修复的文件
            fix_files_query = """
            SELECT DISTINCT
                f.id,
                f.filename
            FROM files f
            JOIN favorites fav ON f.id = fav.file_id
            WHERE fav.user_id = %s AND (f.is_image = 0 OR f.is_image IS NULL)
            """
            
            fix_files = db.fetch_all(fix_files_query, (user_id,))
            
            image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tif', '.tiff', '.psd', '.ai', '.eps']
            
            for file in fix_files:
                filename = file['filename'].lower()
                is_image = any(filename.endswith(ext) for ext in image_extensions)
                
                if is_image:
                    update_query = "UPDATE files SET is_image = 1 WHERE id = %s"
                    db.execute(update_query, (file['id'],))
                    print(f"  ✅ 修复文件 {file['filename']} 为图片")
        
        # 6. 最终验证
        print(f"\n6. 最终验证:")
        final_query = """
        SELECT 
            f.id as favorite_id,
            f.file_id,
            fi.filename,
            fi.is_image,
            f.is_active
        FROM favorites f
        JOIN files fi ON f.file_id = fi.id
        WHERE f.user_id = %s
        ORDER BY f.favorited_at DESC
        """
        
        final_result = db.fetch_all(final_query, (user_id,))
        print(f"最终收藏记录数: {len(final_result)}")
        
        active_image_count = 0
        for result in final_result:
            if result['is_active'] and result['is_image']:
                active_image_count += 1
            print(f"  收藏ID: {result['favorite_id']}, 文件: {result['filename']}, 图片: {result['is_image']}, 活跃: {result['is_active']}")
        
        print(f"活跃的图片收藏数: {active_image_count}")
        
        print(f"\n✅ 修复完成！现在应该能看到 {active_image_count} 个收藏")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    fix_favorites_data()
