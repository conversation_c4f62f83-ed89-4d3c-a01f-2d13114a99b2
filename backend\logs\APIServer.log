2025-06-10 19:09:35 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-10 19:09:51 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:09:51 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:09:51 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:09:51 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:09:51 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:09:51 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:09:51 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:09:51 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:09:51 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:09:51 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:09:51 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:09:51 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:09:53 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:09:53 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:09:53 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:09:53 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:09:53 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:09:53 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:09:53 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:09:53 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:09:53 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:09:53 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:09:53 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:09:53 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:09:53 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:09:57 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 32
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件ID: 32, 文件信息: {'id': 32, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本 - 副本.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:09:57 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:09:57 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:09:57 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 16
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件ID: 16, 文件信息: {'id': 16, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本 - 副本.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:09:57 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:09:57 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:09:57 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 14
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件ID: 14, 文件信息: {'id': 14, 'folder_id': 4, 'filename': '3 - 副本 (10).jpg', 'relative_path': '3 - 副本 (10).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10).jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10).jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:09:57 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10).jpg, 尺寸: medium
2025-06-10 19:09:57 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:09:57 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 11
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件ID: 11, 文件信息: {'id': 11, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:09:57 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:09:57 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:09:57 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 9
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件ID: 9, 文件信息: {'id': 9, 'folder_id': 4, 'filename': '3.jpg', 'relative_path': '3.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T18:26:01', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件名: 3.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:09:57 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:09:57 - APIServer - INFO - 开始生成缩略图: D:\测试2\3.jpg, 尺寸: medium
2025-06-10 19:09:57 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:09:57 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:09:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:09:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:09:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:09:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:09:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:09:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:09:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:09:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:09:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:10:03 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 32
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件ID: 32, 文件信息: {'id': 32, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本 - 副本.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:10:03 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:10:03 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:10:03 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 16
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件ID: 16, 文件信息: {'id': 16, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本 - 副本.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:10:03 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:10:03 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:10:03 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 14
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件ID: 14, 文件信息: {'id': 14, 'folder_id': 4, 'filename': '3 - 副本 (10).jpg', 'relative_path': '3 - 副本 (10).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10).jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10).jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:10:03 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10).jpg, 尺寸: medium
2025-06-10 19:10:03 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:10:03 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 11
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件ID: 11, 文件信息: {'id': 11, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:10:03 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:10:03 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:10:03 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 9
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件ID: 9, 文件信息: {'id': 9, 'folder_id': 4, 'filename': '3.jpg', 'relative_path': '3.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T18:26:01', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件名: 3.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:10:03 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:10:03 - APIServer - INFO - 开始生成缩略图: D:\测试2\3.jpg, 尺寸: medium
2025-06-10 19:10:03 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:10:03 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:10:11 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:10:11 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:10:11 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:10:11 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:10:11 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:10:11 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:10:11 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:10:11 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:10:11 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:10:32 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:10:32 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:10:32 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:10:34 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:10:34 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:10:34 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:10:35 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:10:35 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:10:35 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:10:35 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:10:35 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:10:35 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:10:35 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:10:35 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:10:35 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:15:19 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-10 19:15:23 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:15:23 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:15:23 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:15:23 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:15:23 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:15:23 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:15:23 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:15:23 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:15:23 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:15:23 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:15:23 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:15:23 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:15:24 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:15:24 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:24 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:15:24 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:15:24 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:15:24 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:15:24 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:24 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:15:24 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:24 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:15:24 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:15:24 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:15:24 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:15:27 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:15:27 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:15:27 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:15:27 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:15:27 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:15:27 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:15:27 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:15:27 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:15:27 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 9
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 9, 文件信息: {'id': 9, 'folder_id': 4, 'filename': '3.jpg', 'relative_path': '3.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T18:26:01', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 11
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 11, 文件信息: {'id': 11, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 13
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 13, 文件信息: {'id': 13, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\830d0b30fe5c4caeb0bbb6dbb60e984d_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\830d0b30fe5c4caeb0bbb6dbb60e984d_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\830d0b30fe5c4caeb0bbb6dbb60e984d_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 12
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 12, 文件信息: {'id': 12, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d9c2f08712ad4744eef5aac8bb0b3f12_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d9c2f08712ad4744eef5aac8bb0b3f12_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d9c2f08712ad4744eef5aac8bb0b3f12_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 14
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 14, 文件信息: {'id': 14, 'folder_id': 4, 'filename': '3 - 副本 (10).jpg', 'relative_path': '3 - 副本 (10).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10).jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10).jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10).jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 15
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 15, 文件信息: {'id': 15, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\8b922b93c487c2f6d0c87a79c4718dac_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8b922b93c487c2f6d0c87a79c4718dac_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8b922b93c487c2f6d0c87a79c4718dac_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 16
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 16, 文件信息: {'id': 16, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 17
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 17, 文件信息: {'id': 17, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\7b5de39498aacc4574a9b9168f3f2116_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7b5de39498aacc4574a9b9168f3f2116_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7b5de39498aacc4574a9b9168f3f2116_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 18
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 18, 文件信息: {'id': 18, 'folder_id': 4, 'filename': '3 - 副本 (11).jpg', 'relative_path': '3 - 副本 (11).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11).jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11).jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11).jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d88afa30afe6d2552b6f6400f4860e7e_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d88afa30afe6d2552b6f6400f4860e7e_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d88afa30afe6d2552b6f6400f4860e7e_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 19
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 19, 文件信息: {'id': 19, 'folder_id': 4, 'filename': '3 - 副本 (12) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (12) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (12) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (12) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (12) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (12) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\0aff45f1acf1fb0c9eb59d16a6418372_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0aff45f1acf1fb0c9eb59d16a6418372_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0aff45f1acf1fb0c9eb59d16a6418372_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 20
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 20, 文件信息: {'id': 20, 'folder_id': 4, 'filename': '3 - 副本 (12) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (12) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (12) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (12) - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (12) - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (12) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c6c26eeac0bad86fd939e6a7ce5a72ea_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c6c26eeac0bad86fd939e6a7ce5a72ea_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c6c26eeac0bad86fd939e6a7ce5a72ea_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 21
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 21, 文件信息: {'id': 21, 'folder_id': 4, 'filename': '3 - 副本 (12) - 副本.jpg', 'relative_path': '3 - 副本 (12) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (12) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (12) - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (12) - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (12) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\dd0d28c6962026b7fc4a923ec91bdc74_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\dd0d28c6962026b7fc4a923ec91bdc74_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\dd0d28c6962026b7fc4a923ec91bdc74_medium.jpg
2025-06-10 19:15:30 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 22
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件ID: 22, 文件信息: {'id': 22, 'folder_id': 4, 'filename': '3 - 副本 (13) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (13) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (13) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (13) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:30 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (13) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:30 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (13) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:30 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\0ed21dea8ec3000e0627be81ab39c9fa_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0ed21dea8ec3000e0627be81ab39c9fa_medium.jpg
2025-06-10 19:15:30 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0ed21dea8ec3000e0627be81ab39c9fa_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 23
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 23, 文件信息: {'id': 23, 'folder_id': 4, 'filename': '3 - 副本 (13) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (13) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (13) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (13) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (13) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (13) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\57756ed7d7c1f7026e0d45a00e5926b9_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\57756ed7d7c1f7026e0d45a00e5926b9_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\57756ed7d7c1f7026e0d45a00e5926b9_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 24
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 24, 文件信息: {'id': 24, 'folder_id': 4, 'filename': '3 - 副本 (13) - 副本.jpg', 'relative_path': '3 - 副本 (13) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (13) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (13) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (13) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (13) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\046b1e4fcd9f408e3824d628a0764123_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\046b1e4fcd9f408e3824d628a0764123_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\046b1e4fcd9f408e3824d628a0764123_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 25
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 25, 文件信息: {'id': 25, 'folder_id': 4, 'filename': '3 - 副本 (14) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (14) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (14) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (14) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (14) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (14) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d2893cf50f58df4ef6879da35725da63_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d2893cf50f58df4ef6879da35725da63_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d2893cf50f58df4ef6879da35725da63_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 26
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 26, 文件信息: {'id': 26, 'folder_id': 4, 'filename': '3 - 副本 (14) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (14) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (14) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (14) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (14) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (14) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2746bd5f20f8614a623b7f0be3178561_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2746bd5f20f8614a623b7f0be3178561_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2746bd5f20f8614a623b7f0be3178561_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 27
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 27, 文件信息: {'id': 27, 'folder_id': 4, 'filename': '3 - 副本 (14) - 副本.jpg', 'relative_path': '3 - 副本 (14) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (14) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (14) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (14) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (14) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\3cf78ed2eb5050e3263665a5ba1addb2_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cf78ed2eb5050e3263665a5ba1addb2_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cf78ed2eb5050e3263665a5ba1addb2_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 28
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 28, 文件信息: {'id': 28, 'folder_id': 4, 'filename': '3 - 副本 (15) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (15) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (15) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (15) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (15) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (15) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2ba5f6f3f1c8725d3e7fc07ef7d02edb_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2ba5f6f3f1c8725d3e7fc07ef7d02edb_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2ba5f6f3f1c8725d3e7fc07ef7d02edb_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 29
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 29, 文件信息: {'id': 29, 'folder_id': 4, 'filename': '3 - 副本 (15) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (15) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (15) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (15) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (15) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (15) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\012a765d5cd6170a42f30117f9415784_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\012a765d5cd6170a42f30117f9415784_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\012a765d5cd6170a42f30117f9415784_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 31
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 31, 文件信息: {'id': 31, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f00d49302bea9d9b885d84df38c40ee4_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f00d49302bea9d9b885d84df38c40ee4_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f00d49302bea9d9b885d84df38c40ee4_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 30
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 30, 文件信息: {'id': 30, 'folder_id': 4, 'filename': '3 - 副本 (15) - 副本.jpg', 'relative_path': '3 - 副本 (15) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (15) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (15) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (15) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (15) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\16d56b05cec65b78790cf0f244a56c01_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\16d56b05cec65b78790cf0f244a56c01_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\16d56b05cec65b78790cf0f244a56c01_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 32
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 32, 文件信息: {'id': 32, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 33
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 33, 文件信息: {'id': 33, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\79f8b1ee1c99bcfd449bd1bf51d981cf_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\79f8b1ee1c99bcfd449bd1bf51d981cf_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\79f8b1ee1c99bcfd449bd1bf51d981cf_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 34
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 34, 文件信息: {'id': 34, 'folder_id': 4, 'filename': '3 - 副本 (17) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (17) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (17) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (17) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (17) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (17) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ed36689f714899e7f6825bc6c76584c3_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ed36689f714899e7f6825bc6c76584c3_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ed36689f714899e7f6825bc6c76584c3_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 35
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 35, 文件信息: {'id': 35, 'folder_id': 4, 'filename': '3 - 副本 (17) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (17) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (17) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (17) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (17) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (17) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\587ff386c900b5eb6feb7c47136a903e_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\587ff386c900b5eb6feb7c47136a903e_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\587ff386c900b5eb6feb7c47136a903e_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 36
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 36, 文件信息: {'id': 36, 'folder_id': 4, 'filename': '3 - 副本 (17) - 副本.jpg', 'relative_path': '3 - 副本 (17) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (17) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (17) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (17) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (17) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\68320b334d4b6ab9fba2f921f14d559f_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\68320b334d4b6ab9fba2f921f14d559f_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\68320b334d4b6ab9fba2f921f14d559f_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 37
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 37, 文件信息: {'id': 37, 'folder_id': 4, 'filename': '3 - 副本 (18) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (18) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (18) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (18) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (18) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (18) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\aeb3b8190bd9d511701e57070714ce45_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aeb3b8190bd9d511701e57070714ce45_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aeb3b8190bd9d511701e57070714ce45_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 39
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 39, 文件信息: {'id': 39, 'folder_id': 4, 'filename': '3 - 副本 (18) - 副本.jpg', 'relative_path': '3 - 副本 (18) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (18) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (18) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (18) - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (18) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\4f85f562f0eeb9d4d77d479fde830750_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\4f85f562f0eeb9d4d77d479fde830750_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\4f85f562f0eeb9d4d77d479fde830750_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 40
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 40, 文件信息: {'id': 40, 'folder_id': 4, 'filename': '3 - 副本 (19) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (19) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (19) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (19) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (19) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (19) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\8f5e5b0657e5abf39b3cceced7d24d14_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8f5e5b0657e5abf39b3cceced7d24d14_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8f5e5b0657e5abf39b3cceced7d24d14_medium.jpg
2025-06-10 19:15:31 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 38
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件ID: 38, 文件信息: {'id': 38, 'folder_id': 4, 'filename': '3 - 副本 (18) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (18) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (18) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (18) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:31 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (18) - 副本 - 副本.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:31 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (18) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:31 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\589e9302eb8a3ff655894f2ae492726c_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\589e9302eb8a3ff655894f2ae492726c_medium.jpg
2025-06-10 19:15:31 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\589e9302eb8a3ff655894f2ae492726c_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 41
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 41, 文件信息: {'id': 41, 'folder_id': 4, 'filename': '3 - 副本 (19) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (19) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (19) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (19) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (19) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (19) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\fd7c070a2a5d88a2ff9410243d2ab01a_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fd7c070a2a5d88a2ff9410243d2ab01a_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fd7c070a2a5d88a2ff9410243d2ab01a_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 42
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 42, 文件信息: {'id': 42, 'folder_id': 4, 'filename': '3 - 副本 (19) - 副本.jpg', 'relative_path': '3 - 副本 (19) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (19) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (19) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (19) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (19) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\a14bafdec39e39159641f49061fd8aa5_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a14bafdec39e39159641f49061fd8aa5_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a14bafdec39e39159641f49061fd8aa5_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 44
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 44, 文件信息: {'id': 44, 'folder_id': 4, 'filename': '3 - 副本 (2) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (2) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (2) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\aaa5464dc3f938d9b1c932b91e59f407_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aaa5464dc3f938d9b1c932b91e59f407_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aaa5464dc3f938d9b1c932b91e59f407_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 43
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 43, 文件信息: {'id': 43, 'folder_id': 4, 'filename': '3 - 副本 (2) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (2) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (2) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c7020ff92ba16ce195c616344c960ed6_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c7020ff92ba16ce195c616344c960ed6_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c7020ff92ba16ce195c616344c960ed6_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 45
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 45, 文件信息: {'id': 45, 'folder_id': 4, 'filename': '3 - 副本 (2) - 副本.jpg', 'relative_path': '3 - 副本 (2) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (2) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\40ca54c51a97504ccca9d5057d97e609_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\40ca54c51a97504ccca9d5057d97e609_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\40ca54c51a97504ccca9d5057d97e609_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 46
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 46, 文件信息: {'id': 46, 'folder_id': 4, 'filename': '3 - 副本 (2).jpg', 'relative_path': '3 - 副本 (2).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (2).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2).jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2).jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2).jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\59b2961926dcfa3f564a72391d7e986c_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\59b2961926dcfa3f564a72391d7e986c_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\59b2961926dcfa3f564a72391d7e986c_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 47
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 47, 文件信息: {'id': 47, 'folder_id': 4, 'filename': '3 - 副本 (20) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (20) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (20) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (20) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (20) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (20) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6fc2f6a250fa0227a1761a5f4d600e85_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6fc2f6a250fa0227a1761a5f4d600e85_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6fc2f6a250fa0227a1761a5f4d600e85_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 48
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 48, 文件信息: {'id': 48, 'folder_id': 4, 'filename': '3 - 副本 (20) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (20) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (20) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (20) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (20) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (20) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\9f2dc639240f135864d7da2e3450403b_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9f2dc639240f135864d7da2e3450403b_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9f2dc639240f135864d7da2e3450403b_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 50
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 50, 文件信息: {'id': 50, 'folder_id': 4, 'filename': '3 - 副本 (21) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (21) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (21) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (21) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (21) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (21) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\44cd2840a5e895d8e50b6040f9cf1633_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\44cd2840a5e895d8e50b6040f9cf1633_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\44cd2840a5e895d8e50b6040f9cf1633_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 49
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 49, 文件信息: {'id': 49, 'folder_id': 4, 'filename': '3 - 副本 (20) - 副本.jpg', 'relative_path': '3 - 副本 (20) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (20) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (20) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (20) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (20) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\10903216d7fd541d368d66adc7c4664b_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\10903216d7fd541d368d66adc7c4664b_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\10903216d7fd541d368d66adc7c4664b_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 52
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 52, 文件信息: {'id': 52, 'folder_id': 4, 'filename': '3 - 副本 (21) - 副本.jpg', 'relative_path': '3 - 副本 (21) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (21) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (21) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (21) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (21) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\bc9b519211f5171b092be24b7767c59b_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\bc9b519211f5171b092be24b7767c59b_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\bc9b519211f5171b092be24b7767c59b_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 51
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 51, 文件信息: {'id': 51, 'folder_id': 4, 'filename': '3 - 副本 (21) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (21) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (21) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (21) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (21) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (21) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f53be811687937a510f6825deb6cbb2e_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f53be811687937a510f6825deb6cbb2e_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f53be811687937a510f6825deb6cbb2e_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 53
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 53, 文件信息: {'id': 53, 'folder_id': 4, 'filename': '3 - 副本 (22) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (22) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (22) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (22) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (22) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (22) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\7730620ba3e208b5b4437bb982430606_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7730620ba3e208b5b4437bb982430606_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7730620ba3e208b5b4437bb982430606_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 54
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 54, 文件信息: {'id': 54, 'folder_id': 4, 'filename': '3 - 副本 (22) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (22) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (22) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (22) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (22) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (22) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f2e270a043a126a37430bdb4a8387bc6_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f2e270a043a126a37430bdb4a8387bc6_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f2e270a043a126a37430bdb4a8387bc6_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 55
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 55, 文件信息: {'id': 55, 'folder_id': 4, 'filename': '3 - 副本 (22) - 副本.jpg', 'relative_path': '3 - 副本 (22) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (22) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (22) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (22) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (22) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ff5d06397dbb96c07e9185356c09e5de_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ff5d06397dbb96c07e9185356c09e5de_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ff5d06397dbb96c07e9185356c09e5de_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 56
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 56, 文件信息: {'id': 56, 'folder_id': 4, 'filename': '3 - 副本 (23) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (23) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (23) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (23) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (23) - 副本 - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (23) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ef0a79b6af41a199715a5298677b8c87_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ef0a79b6af41a199715a5298677b8c87_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ef0a79b6af41a199715a5298677b8c87_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 57
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 57, 文件信息: {'id': 57, 'folder_id': 4, 'filename': '3 - 副本 (23) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (23) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (23) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (23) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (23) - 副本 - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (23) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\11c22a2a7d0e8e14f705d9e5d6fc0e88_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\11c22a2a7d0e8e14f705d9e5d6fc0e88_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\11c22a2a7d0e8e14f705d9e5d6fc0e88_medium.jpg
2025-06-10 19:15:32 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 58
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件ID: 58, 文件信息: {'id': 58, 'folder_id': 4, 'filename': '3 - 副本 (23) - 副本.jpg', 'relative_path': '3 - 副本 (23) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (23) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (23) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:32 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (23) - 副本.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:32 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (23) - 副本.jpg, 尺寸: medium
2025-06-10 19:15:32 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\36a786fedbae4cf6806d4f9be1a52708_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\36a786fedbae4cf6806d4f9be1a52708_medium.jpg
2025-06-10 19:15:32 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\36a786fedbae4cf6806d4f9be1a52708_medium.jpg
2025-06-10 19:15:33 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 10
2025-06-10 19:15:33 - APIServer - INFO - 缩略图请求 - 文件ID: 10, 文件信息: {'id': 10, 'folder_id': 4, 'filename': 'wechat_2025-06-08_191023_815.png', 'relative_path': 'wechat_2025-06-08_191023_815.png', 'file_size': 84610, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-08T19:10:33', 'created_at': '2025-06-08T19:10:49', 'last_accessed': None}, 'full_path': 'D:\\测试2\\wechat_2025-06-08_191023_815.png', 'current_size': 84610, 'current_modified': '2025-06-08T19:10:33.017932', 'exists': True}
2025-06-10 19:15:33 - APIServer - INFO - 缩略图请求 - 文件名: wechat_2025-06-08_191023_815.png
2025-06-10 19:15:33 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:15:33 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:15:33 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\wechat_2025-06-08_191023_815.png
2025-06-10 19:15:33 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:15:33 - APIServer - INFO - 开始生成缩略图: D:\测试2\wechat_2025-06-08_191023_815.png, 尺寸: medium
2025-06-10 19:15:33 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\82b4852d83cf7ec68f0fce4f15e526dd_medium.jpg
2025-06-10 19:15:33 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\82b4852d83cf7ec68f0fce4f15e526dd_medium.jpg
2025-06-10 19:15:33 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\82b4852d83cf7ec68f0fce4f15e526dd_medium.jpg
2025-06-10 19:15:40 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:15:40 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:15:40 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:15:40 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:15:40 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:15:40 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:32 - APIServer - INFO - API服务器已停止
2025-06-10 19:18:39 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-10 19:18:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:43 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:43 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:43 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:50 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:50 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:50 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:50 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:50 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:50 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:50 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:50 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:50 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:52 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:18:52 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:18:52 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:18:52 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:18:52 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:18:52 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:18:52 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:18:52 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:18:52 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:18:52 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:18:52 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:18:52 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:18:52 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:18:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:18:59 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:18:59 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:18:59 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:19:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:19:04 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:19:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:19:04 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:19:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:19:04 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:14 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:19:14 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:19:14 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:14 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:19:14 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:19:14 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:14 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:19:14 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:19:14 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:15 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:19:15 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:15 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:19:15 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:15 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:15 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:19:15 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:15 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:19:15 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:19:15 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:19:15 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:19:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:19:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:19:17 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:19:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:19:17 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:17 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:19:17 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:19:17 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:19:18 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 9
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件ID: 9, 文件信息: {'id': 9, 'folder_id': 4, 'filename': '3.jpg', 'relative_path': '3.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T18:26:01', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件名: 3.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:18 - APIServer - INFO - 开始生成缩略图: D:\测试2\3.jpg, 尺寸: medium
2025-06-10 19:19:18 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\af12c3cf128fc7124310efca8f2bbb72_medium.jpg
2025-06-10 19:19:18 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 12
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件ID: 12, 文件信息: {'id': 12, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本 - 副本.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本 - 副本.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:18 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:18 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d9c2f08712ad4744eef5aac8bb0b3f12_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d9c2f08712ad4744eef5aac8bb0b3f12_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d9c2f08712ad4744eef5aac8bb0b3f12_medium.jpg
2025-06-10 19:19:18 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 11
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件ID: 11, 文件信息: {'id': 11, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:18 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:18 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\22496b31b2684a9859d29654333dc5be_medium.jpg
2025-06-10 19:19:18 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 13
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件ID: 13, 文件信息: {'id': 13, 'folder_id': 4, 'filename': '3 - 副本 (10) - 副本.jpg', 'relative_path': '3 - 副本 (10) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10) - 副本.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10) - 副本.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:18 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:18 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\830d0b30fe5c4caeb0bbb6dbb60e984d_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\830d0b30fe5c4caeb0bbb6dbb60e984d_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\830d0b30fe5c4caeb0bbb6dbb60e984d_medium.jpg
2025-06-10 19:19:18 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 14
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件ID: 14, 文件信息: {'id': 14, 'folder_id': 4, 'filename': '3 - 副本 (10).jpg', 'relative_path': '3 - 副本 (10).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (10).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (10).jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (10).jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:18 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (10).jpg, 尺寸: medium
2025-06-10 19:19:18 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6748de71a3644ace73e3b1f743c2d65b_medium.jpg
2025-06-10 19:19:18 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 15
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件ID: 15, 文件信息: {'id': 15, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:18 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:18 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:18 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\8b922b93c487c2f6d0c87a79c4718dac_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8b922b93c487c2f6d0c87a79c4718dac_medium.jpg
2025-06-10 19:19:18 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8b922b93c487c2f6d0c87a79c4718dac_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 16
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 16, 文件信息: {'id': 16, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\358ff3fc5975f139b41386495f00b1d0_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 17
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 17, 文件信息: {'id': 17, 'folder_id': 4, 'filename': '3 - 副本 (11) - 副本.jpg', 'relative_path': '3 - 副本 (11) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\7b5de39498aacc4574a9b9168f3f2116_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7b5de39498aacc4574a9b9168f3f2116_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7b5de39498aacc4574a9b9168f3f2116_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 18
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 18, 文件信息: {'id': 18, 'folder_id': 4, 'filename': '3 - 副本 (11).jpg', 'relative_path': '3 - 副本 (11).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (11).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (11).jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (11).jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (11).jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d88afa30afe6d2552b6f6400f4860e7e_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d88afa30afe6d2552b6f6400f4860e7e_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d88afa30afe6d2552b6f6400f4860e7e_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 19
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 19, 文件信息: {'id': 19, 'folder_id': 4, 'filename': '3 - 副本 (12) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (12) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (12) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (12) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (12) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (12) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\0aff45f1acf1fb0c9eb59d16a6418372_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0aff45f1acf1fb0c9eb59d16a6418372_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0aff45f1acf1fb0c9eb59d16a6418372_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 20
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 20, 文件信息: {'id': 20, 'folder_id': 4, 'filename': '3 - 副本 (12) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (12) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (12) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (12) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (12) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (12) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c6c26eeac0bad86fd939e6a7ce5a72ea_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c6c26eeac0bad86fd939e6a7ce5a72ea_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c6c26eeac0bad86fd939e6a7ce5a72ea_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 21
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 21, 文件信息: {'id': 21, 'folder_id': 4, 'filename': '3 - 副本 (12) - 副本.jpg', 'relative_path': '3 - 副本 (12) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (12) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (12) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (12) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (12) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\dd0d28c6962026b7fc4a923ec91bdc74_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\dd0d28c6962026b7fc4a923ec91bdc74_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\dd0d28c6962026b7fc4a923ec91bdc74_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 22
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 22, 文件信息: {'id': 22, 'folder_id': 4, 'filename': '3 - 副本 (13) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (13) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (13) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (13) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (13) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (13) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\0ed21dea8ec3000e0627be81ab39c9fa_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0ed21dea8ec3000e0627be81ab39c9fa_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\0ed21dea8ec3000e0627be81ab39c9fa_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 23
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 23, 文件信息: {'id': 23, 'folder_id': 4, 'filename': '3 - 副本 (13) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (13) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (13) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (13) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (13) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (13) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\57756ed7d7c1f7026e0d45a00e5926b9_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\57756ed7d7c1f7026e0d45a00e5926b9_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\57756ed7d7c1f7026e0d45a00e5926b9_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 24
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 24, 文件信息: {'id': 24, 'folder_id': 4, 'filename': '3 - 副本 (13) - 副本.jpg', 'relative_path': '3 - 副本 (13) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (13) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (13) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (13) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (13) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\046b1e4fcd9f408e3824d628a0764123_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\046b1e4fcd9f408e3824d628a0764123_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\046b1e4fcd9f408e3824d628a0764123_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 25
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 25, 文件信息: {'id': 25, 'folder_id': 4, 'filename': '3 - 副本 (14) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (14) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (14) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (14) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (14) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (14) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\d2893cf50f58df4ef6879da35725da63_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d2893cf50f58df4ef6879da35725da63_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\d2893cf50f58df4ef6879da35725da63_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 26
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 26, 文件信息: {'id': 26, 'folder_id': 4, 'filename': '3 - 副本 (14) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (14) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (14) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (14) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (14) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (14) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2746bd5f20f8614a623b7f0be3178561_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2746bd5f20f8614a623b7f0be3178561_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2746bd5f20f8614a623b7f0be3178561_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 27
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 27, 文件信息: {'id': 27, 'folder_id': 4, 'filename': '3 - 副本 (14) - 副本.jpg', 'relative_path': '3 - 副本 (14) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (14) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (14) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (14) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (14) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\3cf78ed2eb5050e3263665a5ba1addb2_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cf78ed2eb5050e3263665a5ba1addb2_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\3cf78ed2eb5050e3263665a5ba1addb2_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 28
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 28, 文件信息: {'id': 28, 'folder_id': 4, 'filename': '3 - 副本 (15) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (15) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (15) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (15) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (15) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (15) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2ba5f6f3f1c8725d3e7fc07ef7d02edb_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2ba5f6f3f1c8725d3e7fc07ef7d02edb_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2ba5f6f3f1c8725d3e7fc07ef7d02edb_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 29
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 29, 文件信息: {'id': 29, 'folder_id': 4, 'filename': '3 - 副本 (15) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (15) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (15) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (15) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (15) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (15) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\012a765d5cd6170a42f30117f9415784_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\012a765d5cd6170a42f30117f9415784_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\012a765d5cd6170a42f30117f9415784_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 30
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 30, 文件信息: {'id': 30, 'folder_id': 4, 'filename': '3 - 副本 (15) - 副本.jpg', 'relative_path': '3 - 副本 (15) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (15) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (15) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (15) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (15) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\16d56b05cec65b78790cf0f244a56c01_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\16d56b05cec65b78790cf0f244a56c01_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\16d56b05cec65b78790cf0f244a56c01_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 31
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 31, 文件信息: {'id': 31, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f00d49302bea9d9b885d84df38c40ee4_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f00d49302bea9d9b885d84df38c40ee4_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f00d49302bea9d9b885d84df38c40ee4_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 32
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 32, 文件信息: {'id': 32, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\2c74b0a358cac1f9e72a5d2fc70338d8_medium.jpg
2025-06-10 19:19:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 33
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件ID: 33, 文件信息: {'id': 33, 'folder_id': 4, 'filename': '3 - 副本 (16) - 副本.jpg', 'relative_path': '3 - 副本 (16) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (16) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (16) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:19 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (16) - 副本.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:19 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (16) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\79f8b1ee1c99bcfd449bd1bf51d981cf_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\79f8b1ee1c99bcfd449bd1bf51d981cf_medium.jpg
2025-06-10 19:19:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\79f8b1ee1c99bcfd449bd1bf51d981cf_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 34
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 34, 文件信息: {'id': 34, 'folder_id': 4, 'filename': '3 - 副本 (17) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (17) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (17) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (17) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (17) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (17) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ed36689f714899e7f6825bc6c76584c3_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ed36689f714899e7f6825bc6c76584c3_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ed36689f714899e7f6825bc6c76584c3_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 35
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 35, 文件信息: {'id': 35, 'folder_id': 4, 'filename': '3 - 副本 (17) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (17) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (17) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (17) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (17) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (17) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\587ff386c900b5eb6feb7c47136a903e_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\587ff386c900b5eb6feb7c47136a903e_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\587ff386c900b5eb6feb7c47136a903e_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 36
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 36, 文件信息: {'id': 36, 'folder_id': 4, 'filename': '3 - 副本 (17) - 副本.jpg', 'relative_path': '3 - 副本 (17) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (17) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (17) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (17) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (17) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\68320b334d4b6ab9fba2f921f14d559f_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\68320b334d4b6ab9fba2f921f14d559f_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\68320b334d4b6ab9fba2f921f14d559f_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 37
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 37, 文件信息: {'id': 37, 'folder_id': 4, 'filename': '3 - 副本 (18) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (18) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (18) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (18) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (18) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (18) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\aeb3b8190bd9d511701e57070714ce45_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aeb3b8190bd9d511701e57070714ce45_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aeb3b8190bd9d511701e57070714ce45_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 39
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 39, 文件信息: {'id': 39, 'folder_id': 4, 'filename': '3 - 副本 (18) - 副本.jpg', 'relative_path': '3 - 副本 (18) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (18) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (18) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (18) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (18) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\4f85f562f0eeb9d4d77d479fde830750_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\4f85f562f0eeb9d4d77d479fde830750_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\4f85f562f0eeb9d4d77d479fde830750_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 38
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 38, 文件信息: {'id': 38, 'folder_id': 4, 'filename': '3 - 副本 (18) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (18) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (18) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (18) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (18) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (18) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\589e9302eb8a3ff655894f2ae492726c_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\589e9302eb8a3ff655894f2ae492726c_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\589e9302eb8a3ff655894f2ae492726c_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 40
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 40, 文件信息: {'id': 40, 'folder_id': 4, 'filename': '3 - 副本 (19) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (19) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (19) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (19) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (19) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (19) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\8f5e5b0657e5abf39b3cceced7d24d14_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8f5e5b0657e5abf39b3cceced7d24d14_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\8f5e5b0657e5abf39b3cceced7d24d14_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 41
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 41, 文件信息: {'id': 41, 'folder_id': 4, 'filename': '3 - 副本 (19) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (19) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (19) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (19) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (19) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (19) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\fd7c070a2a5d88a2ff9410243d2ab01a_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fd7c070a2a5d88a2ff9410243d2ab01a_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\fd7c070a2a5d88a2ff9410243d2ab01a_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 42
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 42, 文件信息: {'id': 42, 'folder_id': 4, 'filename': '3 - 副本 (19) - 副本.jpg', 'relative_path': '3 - 副本 (19) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (19) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (19) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (19) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (19) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\a14bafdec39e39159641f49061fd8aa5_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a14bafdec39e39159641f49061fd8aa5_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\a14bafdec39e39159641f49061fd8aa5_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 43
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 43, 文件信息: {'id': 43, 'folder_id': 4, 'filename': '3 - 副本 (2) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (2) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (2) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\c7020ff92ba16ce195c616344c960ed6_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c7020ff92ba16ce195c616344c960ed6_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\c7020ff92ba16ce195c616344c960ed6_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 44
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 44, 文件信息: {'id': 44, 'folder_id': 4, 'filename': '3 - 副本 (2) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (2) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (2) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\aaa5464dc3f938d9b1c932b91e59f407_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aaa5464dc3f938d9b1c932b91e59f407_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\aaa5464dc3f938d9b1c932b91e59f407_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 45
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 45, 文件信息: {'id': 45, 'folder_id': 4, 'filename': '3 - 副本 (2) - 副本.jpg', 'relative_path': '3 - 副本 (2) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (2) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\40ca54c51a97504ccca9d5057d97e609_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\40ca54c51a97504ccca9d5057d97e609_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\40ca54c51a97504ccca9d5057d97e609_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 46
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 46, 文件信息: {'id': 46, 'folder_id': 4, 'filename': '3 - 副本 (2).jpg', 'relative_path': '3 - 副本 (2).jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (2).jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (2).jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (2).jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (2).jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\59b2961926dcfa3f564a72391d7e986c_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\59b2961926dcfa3f564a72391d7e986c_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\59b2961926dcfa3f564a72391d7e986c_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 47
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 47, 文件信息: {'id': 47, 'folder_id': 4, 'filename': '3 - 副本 (20) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (20) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (20) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (20) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (20) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (20) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6fc2f6a250fa0227a1761a5f4d600e85_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6fc2f6a250fa0227a1761a5f4d600e85_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6fc2f6a250fa0227a1761a5f4d600e85_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 48
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 48, 文件信息: {'id': 48, 'folder_id': 4, 'filename': '3 - 副本 (20) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (20) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (20) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (20) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (20) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (20) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\9f2dc639240f135864d7da2e3450403b_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9f2dc639240f135864d7da2e3450403b_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\9f2dc639240f135864d7da2e3450403b_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 51
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 51, 文件信息: {'id': 51, 'folder_id': 4, 'filename': '3 - 副本 (21) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (21) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (21) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (21) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (21) - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (21) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f53be811687937a510f6825deb6cbb2e_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f53be811687937a510f6825deb6cbb2e_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f53be811687937a510f6825deb6cbb2e_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 50
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 50, 文件信息: {'id': 50, 'folder_id': 4, 'filename': '3 - 副本 (21) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (21) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (21) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (21) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (21) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (21) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\44cd2840a5e895d8e50b6040f9cf1633_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\44cd2840a5e895d8e50b6040f9cf1633_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\44cd2840a5e895d8e50b6040f9cf1633_medium.jpg
2025-06-10 19:19:20 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 49
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件ID: 49, 文件信息: {'id': 49, 'folder_id': 4, 'filename': '3 - 副本 (20) - 副本.jpg', 'relative_path': '3 - 副本 (20) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (20) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (20) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:20 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (20) - 副本.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:20 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (20) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:20 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\10903216d7fd541d368d66adc7c4664b_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\10903216d7fd541d368d66adc7c4664b_medium.jpg
2025-06-10 19:19:20 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\10903216d7fd541d368d66adc7c4664b_medium.jpg
2025-06-10 19:19:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 52
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件ID: 52, 文件信息: {'id': 52, 'folder_id': 4, 'filename': '3 - 副本 (21) - 副本.jpg', 'relative_path': '3 - 副本 (21) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (21) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (21) - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (21) - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (21) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\bc9b519211f5171b092be24b7767c59b_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\bc9b519211f5171b092be24b7767c59b_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\bc9b519211f5171b092be24b7767c59b_medium.jpg
2025-06-10 19:19:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 53
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件ID: 53, 文件信息: {'id': 53, 'folder_id': 4, 'filename': '3 - 副本 (22) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (22) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (22) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (22) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (22) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (22) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\7730620ba3e208b5b4437bb982430606_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7730620ba3e208b5b4437bb982430606_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\7730620ba3e208b5b4437bb982430606_medium.jpg
2025-06-10 19:19:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 54
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件ID: 54, 文件信息: {'id': 54, 'folder_id': 4, 'filename': '3 - 副本 (22) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (22) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (22) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (22) - 副本 - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (22) - 副本 - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (22) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\f2e270a043a126a37430bdb4a8387bc6_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f2e270a043a126a37430bdb4a8387bc6_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\f2e270a043a126a37430bdb4a8387bc6_medium.jpg
2025-06-10 19:19:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 55
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件ID: 55, 文件信息: {'id': 55, 'folder_id': 4, 'filename': '3 - 副本 (22) - 副本.jpg', 'relative_path': '3 - 副本 (22) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (22) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (22) - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (22) - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (22) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ff5d06397dbb96c07e9185356c09e5de_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ff5d06397dbb96c07e9185356c09e5de_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ff5d06397dbb96c07e9185356c09e5de_medium.jpg
2025-06-10 19:19:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 57
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件ID: 57, 文件信息: {'id': 57, 'folder_id': 4, 'filename': '3 - 副本 (23) - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (23) - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (23) - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (23) - 副本 - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (23) - 副本 - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (23) - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\11c22a2a7d0e8e14f705d9e5d6fc0e88_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\11c22a2a7d0e8e14f705d9e5d6fc0e88_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\11c22a2a7d0e8e14f705d9e5d6fc0e88_medium.jpg
2025-06-10 19:19:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 56
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件ID: 56, 文件信息: {'id': 56, 'folder_id': 4, 'filename': '3 - 副本 (23) - 副本 - 副本 - 副本.jpg', 'relative_path': '3 - 副本 (23) - 副本 - 副本 - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (23) - 副本 - 副本 - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (23) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (23) - 副本 - 副本 - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (23) - 副本 - 副本 - 副本.jpg, 尺寸: medium
2025-06-10 19:19:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ef0a79b6af41a199715a5298677b8c87_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ef0a79b6af41a199715a5298677b8c87_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ef0a79b6af41a199715a5298677b8c87_medium.jpg
2025-06-10 19:19:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 58
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件ID: 58, 文件信息: {'id': 58, 'folder_id': 4, 'filename': '3 - 副本 (23) - 副本.jpg', 'relative_path': '3 - 副本 (23) - 副本.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-08T20:51:38', 'last_accessed': None}, 'full_path': 'D:\\测试2\\3 - 副本 (23) - 副本.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件名: 3 - 副本 (23) - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\3 - 副本 (23) - 副本.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\3 - 副本 (23) - 副本.jpg, 尺寸: medium
2025-06-10 19:19:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\36a786fedbae4cf6806d4f9be1a52708_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\36a786fedbae4cf6806d4f9be1a52708_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\36a786fedbae4cf6806d4f9be1a52708_medium.jpg
2025-06-10 19:19:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 10
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件ID: 10, 文件信息: {'id': 10, 'folder_id': 4, 'filename': 'wechat_2025-06-08_191023_815.png', 'relative_path': 'wechat_2025-06-08_191023_815.png', 'file_size': 84610, 'file_hash': None, 'mime_type': None, 'extension': '.png', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-06-08T19:10:33', 'created_at': '2025-06-08T19:10:49', 'last_accessed': None}, 'full_path': 'D:\\测试2\\wechat_2025-06-08_191023_815.png', 'current_size': 84610, 'current_modified': '2025-06-08T19:10:33.017932', 'exists': True}
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件名: wechat_2025-06-08_191023_815.png
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: png, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试2\wechat_2025-06-08_191023_815.png
2025-06-10 19:19:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:21 - APIServer - INFO - 开始生成缩略图: D:\测试2\wechat_2025-06-08_191023_815.png, 尺寸: medium
2025-06-10 19:19:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\82b4852d83cf7ec68f0fce4f15e526dd_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\82b4852d83cf7ec68f0fce4f15e526dd_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\82b4852d83cf7ec68f0fce4f15e526dd_medium.jpg
2025-06-10 19:19:21 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:19:21 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:19:21 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:19:21 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:19:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:20:54 - APIServer - INFO - API服务器已停止
2025-06-10 19:31:40 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-10 19:31:44 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:31:44 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:31:44 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:31:44 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:31:44 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:31:44 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:31:44 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:31:44 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:31:44 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:31:44 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:31:44 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:31:44 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:31:45 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:31:45 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:31:45 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:31:45 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:31:45 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:31:45 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:31:45 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:31:45 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:31:45 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:31:45 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:31:45 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:31:45 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:31:45 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:36:01 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:36:01 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:36:01 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:36:01 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:36:01 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:36:01 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:36:01 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:36:01 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:36:01 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:36:01 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:36:01 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:36:01 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:37:20 - APIServer - INFO - API服务器已停止
2025-06-10 19:37:29 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-10 19:37:33 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:37:33 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:37:33 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:37:33 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:37:33 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:37:33 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:37:33 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:37:33 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:37:33 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:37:33 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:37:33 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:37:33 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:37:36 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:37:36 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:37:36 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:37:36 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:37:36 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:37:36 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:37:36 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:37:36 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:37:36 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:37:36 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:37:36 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:37:36 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:37:38 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:37:38 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:37:38 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:37:38 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:37:38 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:37:38 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:37:38 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:37:38 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:37:38 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:37:38 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:37:38 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:37:38 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:37:38 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:38:00 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:38:00 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:38:00 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:28 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:40:28 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:40:28 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:28 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:40:28 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:40:28 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:28 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:40:28 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:40:28 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:28 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:40:28 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:40:28 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:34 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:40:34 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:40:34 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:40:34 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:40:34 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:40:34 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:40:34 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:40:34 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:40:34 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:40:34 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:40:34 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:40:34 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:40:34 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:40:38 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:40:38 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:40:38 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:41 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:40:41 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:40:41 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:41 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:40:41 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:40:41 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:41 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:40:41 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:40:41 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:41 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:40:41 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:40:41 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:40:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:40:42 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:40:42 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:40:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:40:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:40:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:40:42 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:40:42 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:40:42 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:40:42 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:40:42 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:40:42 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:40:42 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:41:00 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:41:00 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:41:00 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:41:00 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:41:00 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:41:00 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:41:00 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:41:00 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:41:00 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:41:00 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:41:00 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:41:00 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:41:02 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:41:02 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:41:02 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:41:02 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:41:02 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:41:02 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:41:02 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:41:02 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:41:02 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:41:02 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:41:02 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:41:02 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:41:02 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:46:33 - APIServer - INFO - API服务器已停止
2025-06-10 19:46:40 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-10 19:46:47 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:46:47 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:46:47 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:46:47 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:46:47 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:46:47 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:46:47 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:46:47 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:46:47 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:46:47 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:46:47 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:46:47 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:46:51 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:46:51 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:46:51 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:46:51 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:46:51 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:46:51 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:46:51 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:46:51 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:46:51 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:46:51 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:46:51 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:46:51 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:46:51 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:47:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:04 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:04 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:04 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:04 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:04 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:06 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:06 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:06 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:08 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:08 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:08 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:08 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:08 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:08 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:08 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:08 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:08 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:08 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:47:08 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:47:08 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:47:10 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:47:10 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:47:10 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:47:10 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:47:10 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:47:10 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:47:10 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:47:10 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:47:10 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:47:10 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:47:10 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:47:10 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:47:10 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:53:39 - APIServer - INFO - API服务器已停止
2025-06-10 19:53:53 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-10 19:53:57 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:53:57 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:53:57 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:53:57 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:53:57 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:53:57 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:53:57 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:53:57 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:53:57 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:53:57 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-06-10 19:53:57 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 2, 'name': '测试', 'path': 'D:\\测试', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 50608, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:28:29', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:28:29', 'file_count': 2}, {'id': 3, 'name': 'HLDB', 'path': 'D:\\HLDB', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 4, 'total_size': 17790982935, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-07T20:36:34', 'updated_at': '2025-06-07T21:03:56', 'last_scanned': '2025-06-07T20:36:43', 'file_count': 4}, {'id': 4, 'name': '测试2', 'path': 'D:\\测试2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 320, 'total_size': 18691680, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-06-08T18:19:57', 'updated_at': '2025-06-08T20:51:38', 'last_scanned': '2025-06-08T20:51:38', 'file_count': 320}]}
2025-06-10 19:53:57 - APIServer - INFO - 返回 3 个文件夹
2025-06-10 19:53:59 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 3
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 文件ID: 3, 文件信息: {'id': 3, 'folder_id': 2, 'filename': '5efedafd65dce35beead5d6ad884233a.jpeg', 'relative_path': '5efedafd65dce35beead5d6ad884233a.jpeg', 'file_size': 18395, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:00', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\5efedafd65dce35beead5d6ad884233a.jpeg', 'current_size': 18395, 'current_modified': '2025-05-28T23:48:59.585574', 'exists': True}
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 文件名: 5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpeg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg
2025-06-10 19:53:59 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:53:59 - APIServer - INFO - 开始生成缩略图: D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg, 尺寸: medium
2025-06-10 19:53:59 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:53:59 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:53:59 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ebfc0a072451aba9096854a34a4c1106_medium.jpg
2025-06-10 19:53:59 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 4
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 文件ID: 4, 文件信息: {'id': 4, 'folder_id': 2, 'filename': '664ace4d9f28cb621a39679e3d665673.jpg', 'relative_path': '664ace4d9f28cb621a39679e3d665673.jpg', 'file_size': 32213, 'file_hash': None, 'mime_type': None, 'extension': None, 'file_type': {'is_image': False, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': None, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2025-05-28T23:49:23', 'created_at': '2025-06-07T20:28:29', 'last_accessed': None}, 'full_path': 'D:\\测试\\664ace4d9f28cb621a39679e3d665673.jpg', 'current_size': 32213, 'current_modified': '2025-05-28T23:49:23.199260', 'exists': True}
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 文件名: 664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-06-10 19:53:59 - APIServer - INFO - 缩略图请求 - 完整路径: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg
2025-06-10 19:53:59 - APIServer - INFO - 缩略图服务可用: True
2025-06-10 19:53:59 - APIServer - INFO - 开始生成缩略图: D:\测试\664ace4d9f28cb621a39679e3d665673.jpg, 尺寸: medium
2025-06-10 19:53:59 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:53:59 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
2025-06-10 19:53:59 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\ea6c16c6e74a6fae515f2f71363c8312_medium.jpg
