#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务器
"""

from flask import Flask, request, jsonify, send_file, abort
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
import threading
import time
import os
from datetime import datetime
from typing import Dict, Any, Optional

from utils.logger import setup_logger

# 可选导入服务模块
try:
    from services.user_service import UserService
    HAS_USER_SERVICE = True
except ImportError:
    HAS_USER_SERVICE = False

try:
    from services.thumbnail_service import ThumbnailService
    HAS_THUMBNAIL_SERVICE = True
except ImportError:
    HAS_THUMBNAIL_SERVICE = False

try:
    from services.encryption_service import EncryptionService
    HAS_ENCRYPTION_SERVICE = True
except ImportError:
    HAS_ENCRYPTION_SERVICE = False

class APIServer:
    """API服务器类"""

    def __init__(self, services: Dict[str, Any], settings):
        self.services = services
        self.settings = settings
        self.logger = setup_logger("APIServer")

        # 初始化额外服务
        self._initialize_additional_services()

        # 创建Flask应用
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'your-secret-key-here'

        # 启用CORS
        try:
            CORS(self.app)
        except:
            self.logger.warning("CORS模块不可用")

        # 启用SocketIO
        try:
            self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        except:
            self.logger.warning("SocketIO模块不可用")
            self.socketio = None

        # 注册路由
        self.register_routes()
        if self.socketio:
            self.register_socketio_events()

        self.running = False

    def _initialize_additional_services(self):
        """初始化额外服务"""
        try:
            # 初始化用户服务
            if HAS_USER_SERVICE and 'user' not in self.services:
                db_manager = getattr(self, 'db_manager', None)
                if hasattr(self.services.get('file', {}), 'db_manager'):
                    db_manager = self.services['file'].db_manager

                self.services['user'] = UserService(db_manager)
                self.logger.info("用户服务初始化成功")

            # 初始化缩略图服务
            if HAS_THUMBNAIL_SERVICE and 'thumbnail' not in self.services:
                self.services['thumbnail'] = ThumbnailService()
                self.logger.info("缩略图服务初始化成功")

            # 初始化加密服务
            if HAS_ENCRYPTION_SERVICE and 'encryption' not in self.services:
                db_manager = getattr(self, 'db_manager', None)
                if hasattr(self.services.get('file', {}), 'db_manager'):
                    db_manager = self.services['file'].db_manager

                self.services['encryption'] = EncryptionService(db_manager=db_manager)
                self.logger.info("加密服务初始化成功")

        except Exception as e:
            self.logger.error(f"初始化额外服务失败: {e}")
    
    def register_routes(self):
        """注册API路由"""
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                'status': 'ok',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            })
        
        @self.app.route('/api/server/status', methods=['GET'])
        def server_status():
            """获取服务器状态"""
            return jsonify({
                'running': self.running,
                'uptime': time.time() - getattr(self, 'start_time', time.time()),
                'services': list(self.services.keys())
            })
        
        @self.app.route('/api/auth/login', methods=['POST'])
        def login():
            """用户登录"""
            try:
                data = request.get_json()
                username = data.get('username')
                password = data.get('password')

                if not username or not password:
                    return jsonify({'error': '用户名和密码不能为空'}), 400

                # 获取客户端信息
                ip_address = request.remote_addr
                user_agent = request.headers.get('User-Agent')

                # 使用用户服务进行验证
                user_service = self.services.get('user')
                if user_service:
                    result = user_service.authenticate_user(
                        username, password, ip_address, user_agent
                    )

                    if result['success']:
                        # 记录用户登录活动到监控服务
                        monitoring_service = self.services.get('monitoring')
                        if monitoring_service:
                            monitoring_service.record_user_activity(
                                result['user']['id'],
                                'login',
                                {'username': username, 'success': True},
                                ip_address
                            )

                        return jsonify({
                            'success': True,
                            'token': result['session_token'],
                            'user': result['user']
                        })
                    else:
                        # 记录登录失败
                        monitoring_service = self.services.get('monitoring')
                        if monitoring_service:
                            monitoring_service.record_user_activity(
                                0,  # 未知用户ID
                                'login_failed',
                                {'username': username, 'error': result['error']},
                                ip_address
                            )
                        return jsonify({'error': result['error']}), 401
                else:
                    # 临时返回成功响应（用户服务不可用时）
                    return jsonify({
                        'success': True,
                        'token': 'temp-token',
                        'user': {
                            'id': 1,
                            'username': username,
                            'permissions': ['read', 'download']
                        }
                    })

            except Exception as e:
                self.logger.error(f"登录失败: {e}")
                return jsonify({'error': '登录失败'}), 500

        @self.app.route('/api/auth/logout', methods=['POST'])
        def logout():
            """用户登出"""
            try:
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                ip_address = request.remote_addr

                # 获取用户信息用于记录
                user_info = self._validate_token(token)

                user_service = self.services.get('user')
                if user_service and token:
                    user_service.logout_user(token)

                # 记录登出活动
                if user_info:
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_info.get('user_id'),
                            'logout',
                            {'username': user_info.get('username')},
                            ip_address
                        )

                return jsonify({'success': True})

            except Exception as e:
                self.logger.error(f"登出失败: {e}")
                return jsonify({'error': '登出失败'}), 500

        @self.app.route('/api/auth/verify', methods=['GET'])
        def verify_token():
            """验证token有效性"""
            try:
                token = request.headers.get('Authorization', '').replace('Bearer ', '')

                if not token:
                    return jsonify({'valid': False, 'error': '缺少token'}), 401

                user_info = self._validate_token(token)
                if user_info:
                    return jsonify({
                        'valid': True,
                        'user': {
                            'id': user_info.get('user_id'),
                            'username': user_info.get('username'),
                            'permissions': user_info.get('permissions', []),
                            'is_admin': user_info.get('is_admin', False)
                        }
                    })
                else:
                    return jsonify({'valid': False, 'error': 'token无效或已过期'}), 401

            except Exception as e:
                self.logger.error(f"token验证失败: {e}")
                return jsonify({'valid': False, 'error': 'token验证失败'}), 500
        
        @self.app.route('/api/files/folders', methods=['GET'])
        def get_shared_folders():
            """获取共享文件夹列表"""
            try:
                self.logger.info("收到获取共享文件夹列表请求")

                file_service = self.services.get('file')
                if not file_service:
                    self.logger.error("文件服务不可用")
                    return jsonify({'error': '文件服务不可用'}), 503

                result = file_service.get_shared_folders()
                self.logger.info(f"文件服务返回结果: {result}")

                # 检查结果格式并提取文件夹列表
                if result.get('success', True) and 'folders' in result:
                    folders = result['folders']
                    self.logger.info(f"返回 {len(folders)} 个文件夹")
                    return jsonify(folders)
                else:
                    self.logger.error(f"获取文件夹列表失败: {result.get('error', '未知错误')}")
                    return jsonify([])  # 返回空数组而不是错误

            except Exception as e:
                self.logger.error(f"获取文件夹列表失败: {e}")
                import traceback
                self.logger.error(f"错误详情: {traceback.format_exc()}")
                return jsonify([])  # 返回空数组而不是错误
        
        @self.app.route('/api/files', methods=['GET'])
        def get_files():
            """获取文件列表"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                folder_id = request.args.get('folder_id', type=int)
                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 50, type=int)
                search_query = request.args.get('search', '')

                # 获取文件列表
                if folder_id:
                    result = file_service.get_folder_files(
                        folder_id, page, page_size, search_query
                    )
                else:
                    # 获取根目录文件
                    result = file_service.get_root_files(page, page_size, search_query)

                # 检查结果格式
                if not result.get('success', True):
                    return jsonify({'error': result.get('error', '获取文件失败')}), 500

                # 过滤只显示图片文件
                if 'files' in result:
                    filtered_files = []
                    for file_item in result['files']:
                        # 文件夹总是显示
                        if file_item.get('type') == 'folder':
                            filtered_files.append(file_item)
                        else:
                            # 只显示图片格式文件
                            filename = file_item.get('filename', file_item.get('name', ''))
                            if filename:
                                ext = filename.lower().split('.')[-1] if '.' in filename else ''
                                allowed_exts = ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
                                if ext in allowed_exts:
                                    # 标准化文件数据格式（移除时间信息）
                                    file_data = {
                                        'id': file_item.get('id'),
                                        'name': filename,
                                        'type': 'file',
                                        'size': file_item.get('file_size', 0),
                                        'folder_name': file_item.get('folder_name', ''),
                                        'relative_path': file_item.get('relative_path', '')
                                    }
                                    filtered_files.append(file_data)

                    result['files'] = filtered_files
                    result['total'] = len(filtered_files)

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"获取文件列表失败: {e}")
                return jsonify({'error': '获取文件列表失败'}), 500

        @self.app.route('/api/files/folders/<int:folder_id>/files', methods=['GET'])
        def get_folder_files(folder_id):
            """获取文件夹中的文件"""
            try:
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 50, type=int)
                search_query = request.args.get('search', '')

                result = file_service.get_folder_files(
                    folder_id, page, page_size, search_query
                )

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"获取文件列表失败: {e}")
                return jsonify({'error': '获取文件列表失败'}), 500
        
        @self.app.route('/api/files/<int:file_id>/download', methods=['GET'])
        def download_file(file_id):
            """下载文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    abort(503)

                file_info = file_service.get_file_info(file_id)
                if not file_info:
                    abort(404)

                # 检查文件是否存在
                if not file_info.get('exists', False):
                    abort(404)

                # 使用加密服务处理下载
                encryption_service = self.services.get('encryption')
                if encryption_service:
                    result = encryption_service.create_single_file_package(
                        file_info['full_path'], user_info['user_id']
                    )

                    if result['success']:
                        if result.get('encrypted', False):
                            # 返回加密包信息
                            return jsonify({
                                'encrypted': True,
                                'package_id': os.path.basename(result['package_path']),
                                'message': '文件已加密，请申请解压密码'
                            })
                        else:
                            # 返回原文件
                            try:
                                return send_file(result['package_path'], as_attachment=True)
                            except:
                                return jsonify(file_info)
                    else:
                        return jsonify({'error': result['error']}), 500
                else:
                    # 直接返回文件信息（加密服务不可用时）
                    return jsonify(file_info)

            except Exception as e:
                self.logger.error(f"下载文件失败: {e}")
                abort(500)

        @self.app.route('/api/files/batch/download', methods=['POST'])
        def batch_download():
            """批量下载文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_ids = data.get('file_ids', [])

                if not file_ids:
                    return jsonify({'error': '文件列表为空'}), 400

                # 获取文件路径
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                file_paths = []
                for file_id in file_ids:
                    file_info = file_service.get_file_info(file_id)
                    if file_info and file_info.get('exists', False):
                        file_paths.append(file_info['full_path'])

                if not file_paths:
                    return jsonify({'error': '没有有效的文件'}), 400

                # 创建批量下载包
                encryption_service = self.services.get('encryption')
                if encryption_service:
                    result = encryption_service.create_batch_package(
                        file_paths, user_info['user_id']
                    )

                    if result['success']:
                        if result.get('encrypted', False):
                            return jsonify({
                                'success': True,
                                'encrypted': True,
                                'package_id': os.path.basename(result['package_path']),
                                'file_count': result['file_count'],
                                'message': '文件包已加密，请申请解压密码'
                            })
                        else:
                            return jsonify({
                                'success': True,
                                'encrypted': False,
                                'download_url': f"/api/files/package/{os.path.basename(result['package_path'])}",
                                'file_count': result['file_count']
                            })
                    else:
                        return jsonify({'error': result['error']}), 500
                else:
                    return jsonify({'error': '加密服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"批量下载失败: {e}")
                return jsonify({'error': '批量下载失败'}), 500
        
        @self.app.route('/api/search', methods=['POST'])
        def search_files():
            """搜索文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                query = data.get('query', '')
                search_type = data.get('type', 'image')  # 默认为图片搜索
                limit = data.get('limit', 100)
                ip_address = request.remote_addr

                # 记录搜索活动
                monitoring_service = self.services.get('monitoring')
                if monitoring_service:
                    monitoring_service.record_user_activity(
                        user_info.get('user_id'),
                        'search',
                        {'query': query, 'search_type': search_type},
                        ip_address
                    )

                # 使用文件服务进行搜索
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                # 执行搜索
                search_results = []
                if query.strip():
                    # 获取所有文件并进行搜索
                    all_files_result = file_service.get_root_files(1, 1000, query)

                    if 'files' in all_files_result:
                        for file_item in all_files_result['files']:
                            # 只搜索图片文件，不包含文件夹
                            if file_item.get('type') != 'folder':
                                filename = file_item.get('filename', file_item.get('name', ''))
                                if filename and query.lower() in filename.lower():
                                    # 检查是否为图片格式
                                    ext = filename.lower().split('.')[-1] if '.' in filename else ''
                                    allowed_exts = ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
                                    if ext in allowed_exts:
                                        # 标准化文件数据格式
                                        file_data = {
                                            'id': file_item.get('id'),
                                            'name': filename,
                                            'type': 'file',
                                            'size': file_item.get('file_size', 0),
                                            'folder_name': file_item.get('folder_name', ''),
                                            'relative_path': file_item.get('relative_path', ''),
                                            'extension': f'.{ext}'
                                        }
                                        search_results.append(file_data)

                return jsonify({
                    'files': search_results,
                    'total': len(search_results),
                    'query': query,
                    'search_type': search_type
                })

            except Exception as e:
                self.logger.error(f"搜索失败: {e}")
                return jsonify({'error': '搜索失败'}), 500

        @self.app.route('/api/files/<int:file_id>/thumbnail', methods=['GET'])
        def get_file_thumbnail(file_id):
            """获取文件缩略图"""
            try:
                # 验证用户权限 - 支持从header或URL参数获取token
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                if not token:
                    # 如果header中没有token，尝试从URL参数获取
                    token = request.args.get('token', '')

                # 临时跳过认证检查，用于调试缩略图功能
                if token:
                    user_info = self._validate_token(token)
                    if not user_info:
                        return jsonify({'error': '未授权访问'}), 401
                else:
                    # 如果没有token，暂时允许访问（仅用于调试）
                    self.logger.warning(f"缩略图请求没有token，允许访问用于调试: {file_id}")
                    pass

                size = request.args.get('size', 'medium')  # small, medium, large

                file_service = self.services.get('file')
                if not file_service:
                    abort(503)

                file_info = file_service.get_file_info(file_id)
                self.logger.info(f"缩略图请求 - 文件ID: {file_id}, 文件信息: {file_info}")
                if not file_info:
                    self.logger.warning(f"缩略图请求失败 - 文件不存在: {file_id}")
                    abort(404)

                # 检查是否为图片文件
                filename = file_info.get('filename', '') or file_info.get('name', '') or file_info.get('relative_path', '')
                self.logger.info(f"缩略图请求 - 文件名: {filename}")
                self.logger.info(f"缩略图请求 - 文件信息字段: {list(file_info.keys())}")

                # 如果还是没有文件名，尝试从路径中提取
                if not filename:
                    full_path = file_info.get('full_path', '')
                    if full_path:
                        filename = os.path.basename(full_path)
                        self.logger.info(f"从路径提取文件名: {filename}")

                if not filename:
                    self.logger.warning(f"缩略图请求失败 - 文件名为空: {file_id}")
                    abort(404)

                ext = filename.lower().split('.')[-1] if '.' in filename else ''
                allowed_exts = ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
                self.logger.info(f"缩略图请求 - 文件扩展名: {ext}, 允许的扩展名: {allowed_exts}")
                if ext not in allowed_exts:
                    self.logger.warning(f"缩略图请求失败 - 不支持的文件格式: {ext}")
                    abort(404)

                # 检查文件路径
                full_path = file_info.get('full_path', '')
                self.logger.info(f"缩略图请求 - 完整路径: {full_path}")

                if not full_path or not os.path.exists(full_path):
                    self.logger.warning(f"缩略图请求失败 - 文件路径不存在: {full_path}")
                    abort(404)

                # 生成缩略图
                thumbnail_service = self.services.get('thumbnail')
                self.logger.info(f"缩略图服务可用: {thumbnail_service is not None}")

                if thumbnail_service:
                    self.logger.info(f"开始生成缩略图: {full_path}, 尺寸: {size}")
                    thumbnail_path = thumbnail_service.get_thumbnail(full_path, size)
                    self.logger.info(f"缩略图路径: {thumbnail_path}")

                    if thumbnail_path:
                        # 如果是相对路径，转换为绝对路径
                        if not os.path.isabs(thumbnail_path):
                            # 相对于backend目录
                            backend_dir = os.path.dirname(os.path.dirname(__file__))
                            thumbnail_path = os.path.join(backend_dir, thumbnail_path)
                            self.logger.info(f"转换为绝对路径: {thumbnail_path}")

                        if os.path.exists(thumbnail_path):
                            self.logger.info(f"缩略图生成成功，返回文件: {thumbnail_path}")
                            return send_file(thumbnail_path, mimetype='image/jpeg')
                        else:
                            self.logger.warning(f"缩略图文件不存在: {thumbnail_path}")

                    # 缩略图生成失败，尝试返回原文件（如果是小图片）
                    self.logger.warning(f"缩略图生成失败，尝试返回原文件")
                    if file_info.get('file_size', 0) < 1024 * 1024:  # 小于1MB
                        try:
                            self.logger.info(f"返回原文件: {full_path}")
                            return send_file(full_path)
                        except Exception as e:
                            self.logger.error(f"返回原文件失败: {e}")
                            abort(404)
                    else:
                        self.logger.warning(f"文件过大，无法返回原文件: {file_info.get('file_size', 0)} bytes")
                        abort(404)
                else:
                    self.logger.warning("缩略图服务不可用")
                    # 缩略图服务不可用，返回原文件（如果是小图片）
                    if file_info.get('file_size', 0) < 1024 * 1024:  # 小于1MB
                        try:
                            self.logger.info(f"缩略图服务不可用，返回原文件: {full_path}")
                            return send_file(full_path)
                        except Exception as e:
                            self.logger.error(f"返回原文件失败: {e}")
                            abort(404)
                    else:
                        abort(503)

            except Exception as e:
                self.logger.error(f"获取缩略图失败: {e}")
                abort(500)

        @self.app.route('/api/files/<int:file_id>/preview', methods=['GET'])
        def get_file_preview(file_id):
            """获取文件预览"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    abort(503)

                file_info = file_service.get_file_info(file_id)
                if not file_info:
                    abort(404)

                # 检查文件是否存在
                if not file_info.get('exists', False):
                    abort(404)

                # 检查是否为图片文件
                filename = file_info.get('name', '')
                if not filename:
                    abort(404)

                ext = filename.lower().split('.')[-1] if '.' in filename else ''
                web_safe_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']

                if ext in web_safe_exts:
                    # 直接返回图片文件
                    return send_file(file_info['full_path'])
                else:
                    # 对于其他格式，尝试生成预览
                    thumbnail_service = self.services.get('thumbnail')
                    if thumbnail_service:
                        preview_path = thumbnail_service.get_thumbnail(
                            file_info['full_path'], 'large'
                        )

                        if preview_path and os.path.exists(preview_path):
                            return send_file(preview_path, mimetype='image/jpeg')
                        else:
                            abort(404)
                    else:
                        abort(503)

            except Exception as e:
                self.logger.error(f"获取文件预览失败: {e}")
                abort(500)

        # 新的下载接口
        @self.app.route('/api/download/single/<int:file_id>', methods=['POST'])
        def download_single_file(file_id):
            """单文件下载（压缩包形式）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('id')
                ip_address = request.remote_addr
                result = download_service.prepare_single_file_download(file_id, user_id)

                if result.get('success'):
                    # 记录下载活动
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_id,
                            'download',
                            {
                                'file_id': file_id,
                                'filename': result.get('filename'),
                                'file_size': result.get('file_size'),
                                'is_encrypted': result.get('is_encrypted')
                            },
                            ip_address
                        )

                    return jsonify({
                        'success': True,
                        'data': {
                            'download_id': result.get('file_id'),
                            'filename': result.get('filename'),
                            'file_size': result.get('file_size'),
                            'is_encrypted': result.get('is_encrypted'),
                            'download_url': f"/api/download/file/{os.path.basename(result.get('zip_path'))}",
                            'password_hint': '如需解压密码，请使用密码申请接口' if result.get('is_encrypted') else None
                        }
                    })
                else:
                    return jsonify({'error': result.get('error', '下载准备失败')}), 400

            except Exception as e:
                self.logger.error(f"单文件下载失败: {e}")
                return jsonify({'error': '下载失败'}), 500

        @self.app.route('/api/download/batch', methods=['POST'])
        def download_batch_files():
            """批量文件下载（压缩包形式）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_ids = data.get('file_ids', [])

                if not file_ids:
                    return jsonify({'error': '文件列表为空'}), 400

                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('id')
                result = download_service.prepare_batch_download(file_ids, user_id)

                if result.get('success'):
                    return jsonify({
                        'success': True,
                        'data': {
                            'file_count': result.get('file_count'),
                            'file_size': result.get('file_size'),
                            'is_encrypted': result.get('is_encrypted'),
                            'download_url': f"/api/download/file/{os.path.basename(result.get('zip_path'))}",
                            'encrypted_files': result.get('encrypted_file_ids', []),
                            'password_hint': '如需解压密码，请使用密码申请接口' if result.get('is_encrypted') else None
                        }
                    })
                else:
                    return jsonify({'error': result.get('error', '批量下载准备失败')}), 400

            except Exception as e:
                self.logger.error(f"批量下载失败: {e}")
                return jsonify({'error': '批量下载失败'}), 500

        @self.app.route('/api/download/folder/<int:folder_id>', methods=['GET'])
        def download_folder(folder_id):
            """文件夹下载（压缩包形式）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('id')
                result = download_service.prepare_folder_download(folder_id, user_id)

                if result.get('success'):
                    # 直接返回文件流
                    zip_path = result.get('zip_path')
                    if os.path.exists(zip_path):
                        return send_file(
                            zip_path,
                            as_attachment=True,
                            download_name=f"folder_{folder_id}_{int(time.time())}.zip",
                            mimetype='application/zip'
                        )
                    else:
                        return jsonify({'error': '压缩包生成失败'}), 500
                else:
                    return jsonify({'error': result.get('error', '文件夹下载准备失败')}), 400

            except Exception as e:
                self.logger.error(f"文件夹下载失败: {e}")
                return jsonify({'error': '文件夹下载失败'}), 500

        @self.app.route('/api/download/file/<path:filename>', methods=['GET'])
        def serve_download_file(filename):
            """提供下载文件服务"""
            try:
                download_service = self.services.get('download')
                if not download_service:
                    abort(503)

                file_path = os.path.join(download_service.temp_dir, filename)
                if not os.path.exists(file_path):
                    abort(404)

                return send_file(
                    file_path,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/zip'
                )

            except Exception as e:
                self.logger.error(f"文件下载服务失败: {e}")
                abort(500)

        @self.app.route('/api/download/password/request', methods=['POST'])
        def request_download_password():
            """申请下载密码"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_id = data.get('file_id')
                reason = data.get('reason', '')

                if not file_id:
                    return jsonify({'error': '文件ID不能为空'}), 400

                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('id')
                result = download_service.request_password(file_id, user_id)

                if result.get('success'):
                    return jsonify({
                        'success': True,
                        'data': {
                            'password': result.get('password'),
                            'remaining_requests': result.get('remaining_requests'),
                            'message': '密码申请成功'
                        }
                    })
                else:
                    return jsonify({'error': result.get('error', '密码申请失败')}), 400

            except Exception as e:
                self.logger.error(f"密码申请失败: {e}")
                return jsonify({'error': '密码申请失败'}), 500

        @self.app.route('/api/download/records', methods=['GET'])
        def get_download_records():
            """获取下载记录"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 这里应该从数据库获取下载记录
                # 暂时返回空列表
                return jsonify({
                    'success': True,
                    'data': {
                        'records': [],
                        'total': 0
                    }
                })

            except Exception as e:
                self.logger.error(f"获取下载记录失败: {e}")
                return jsonify({'error': '获取下载记录失败'}), 500

        # 收藏功能API
        @self.app.route('/api/favorites', methods=['GET'])
        def get_user_favorites():
            """获取用户收藏列表"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                favorite_service = self.services.get('favorite')
                if not favorite_service:
                    return jsonify({'error': '收藏服务不可用'}), 503

                user_id = user_info.get('user_id')
                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 50, type=int)
                folder_id = request.args.get('folder_id', type=int)

                result = favorite_service.get_user_favorites(user_id, page, page_size, folder_id)

                if result.get('success'):
                    return jsonify(result['data'])
                else:
                    return jsonify({'error': result.get('error', '获取收藏列表失败')}), 500

            except Exception as e:
                self.logger.error(f"获取收藏列表失败: {e}")
                return jsonify({'error': '获取收藏列表失败'}), 500

        @self.app.route('/api/favorites/toggle', methods=['POST'])
        def toggle_favorite():
            """切换文件收藏状态"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_id = data.get('file_id')
                notes = data.get('notes', '')

                if not file_id:
                    return jsonify({'error': '文件ID不能为空'}), 400

                favorite_service = self.services.get('favorite')
                if not favorite_service:
                    return jsonify({'error': '收藏服务不可用'}), 503

                user_id = user_info.get('user_id')
                result = favorite_service.toggle_favorite(user_id, file_id, notes)

                if result.get('success'):
                    # 记录收藏活动
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_id,
                            'favorite',
                            {
                                'file_id': file_id,
                                'action': result.get('action'),
                                'is_favorited': result.get('is_favorited')
                            },
                            request.remote_addr
                        )

                    return jsonify(result)
                else:
                    return jsonify({'error': result.get('error', '收藏操作失败')}), 400

            except Exception as e:
                self.logger.error(f"收藏操作失败: {e}")
                return jsonify({'error': '收藏操作失败'}), 500

        @self.app.route('/api/favorites/status', methods=['POST'])
        def check_favorite_status():
            """批量检查文件收藏状态"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_ids = data.get('file_ids', [])

                if not file_ids:
                    return jsonify({'error': '文件ID列表不能为空'}), 400

                favorite_service = self.services.get('favorite')
                if not favorite_service:
                    return jsonify({'error': '收藏服务不可用'}), 503

                user_id = user_info.get('user_id')
                result = favorite_service.check_favorite_status(user_id, file_ids)

                if result.get('success'):
                    return jsonify(result['data'])
                else:
                    return jsonify({'error': result.get('error', '检查收藏状态失败')}), 500

            except Exception as e:
                self.logger.error(f"检查收藏状态失败: {e}")
                return jsonify({'error': '检查收藏状态失败'}), 500

        @self.app.route('/api/favorites/stats', methods=['GET'])
        def get_favorite_statistics():
            """获取收藏统计信息"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                favorite_service = self.services.get('favorite')
                if not favorite_service:
                    return jsonify({'error': '收藏服务不可用'}), 503

                user_id = user_info.get('user_id')
                result = favorite_service.get_favorite_statistics(user_id)

                if result.get('success'):
                    return jsonify(result['data'])
                else:
                    return jsonify({'error': result.get('error', '获取收藏统计失败')}), 500

            except Exception as e:
                self.logger.error(f"获取收藏统计失败: {e}")
                return jsonify({'error': '获取收藏统计失败'}), 500

        @self.app.route('/api/admin/users', methods=['GET'])
        def get_users():
            """获取用户列表（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                user_service = self.services.get('user')
                if user_service:
                    page = request.args.get('page', 1, type=int)
                    page_size = request.args.get('page_size', 20, type=int)
                    search_query = request.args.get('search', '')

                    result = user_service.get_user_list(page, page_size, search_query)
                    return jsonify(result)
                else:
                    return jsonify({'users': [], 'total_count': 0})

            except Exception as e:
                self.logger.error(f"获取用户列表失败: {e}")
                return jsonify({'error': '获取用户列表失败'}), 500

        @self.app.route('/api/admin/users', methods=['POST'])
        def create_user():
            """创建用户（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()
                username = data.get('username')
                password = data.get('password')
                email = data.get('email')
                full_name = data.get('full_name')
                user_group = data.get('user_group', 'user')
                is_admin = data.get('is_admin', False)

                if not username or not password:
                    return jsonify({'error': '用户名和密码不能为空'}), 400

                user_service = self.services.get('user')
                if user_service:
                    result = user_service.create_user(
                        username, password, email, full_name, user_group, is_admin
                    )
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"创建用户失败: {e}")
                return jsonify({'error': '创建用户失败'}), 500

        @self.app.route('/api/admin/users/<int:user_id>', methods=['PUT'])
        def update_user(user_id):
            """更新用户信息（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()

                user_service = self.services.get('user')
                if user_service:
                    result = user_service.update_user(user_id, data)
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"更新用户失败: {e}")
                return jsonify({'error': '更新用户失败'}), 500

        @self.app.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
        def delete_user(user_id):
            """删除用户（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                user_service = self.services.get('user')
                if user_service:
                    result = user_service.delete_user(user_id)
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"删除用户失败: {e}")
                return jsonify({'error': '删除用户失败'}), 500
        
        @self.app.route('/api/admin/stats', methods=['GET'])
        def get_statistics():
            """获取系统统计信息"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                # 收集各种统计信息
                stats = {
                    'total_users': 0,
                    'online_users': 0,
                    'total_files': 0,
                    'total_size': 0,
                    'today_downloads': 0,
                    'today_uploads': 0,
                    'today_searches': 0
                }

                # 获取用户统计
                user_service = self.services.get('user')
                if user_service:
                    user_stats = user_service.get_user_statistics()
                    stats.update({
                        'total_users': user_stats.get('total_users', 0),
                        'online_users': user_stats.get('online_users', 0),
                        'active_users': user_stats.get('active_users', 0),
                        'admin_users': user_stats.get('admin_users', 0)
                    })

                # 获取监控统计
                monitoring_service = self.services.get('monitoring')
                if monitoring_service:
                    monitoring_stats = monitoring_service.get_activity_statistics()
                    stats.update({
                        'total_activities': monitoring_stats.get('total_activities', 0),
                        'recent_activities_24h': monitoring_stats.get('recent_activities_24h', 0),
                        'activity_types': monitoring_stats.get('activity_types', {})
                    })

                # 获取文件统计
                file_service = self.services.get('file')
                if file_service:
                    # TODO: 实现文件统计
                    pass

                return jsonify({
                    'success': True,
                    'data': stats
                })

            except Exception as e:
                self.logger.error(f"获取统计信息失败: {e}")
                return jsonify({'error': '获取统计信息失败'}), 500

        @self.app.route('/api/system/info', methods=['GET'])
        def get_system_info():
            """获取系统信息（普通用户可访问）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 基础系统信息
                info = {
                    'version': '1.0.0',
                    'uptime': time.time() - getattr(self, 'start_time', time.time()),
                    'services_count': len(self.services),
                    'status': 'running' if self.running else 'stopped'
                }

                # 获取在线用户数（不显示具体用户信息）
                user_service = self.services.get('user')
                if user_service:
                    user_stats = user_service.get_user_statistics()
                    info['online_users'] = user_stats.get('online_users', 0)

                # 获取存储信息
                monitoring_service = self.services.get('monitoring')
                if monitoring_service:
                    system_status = monitoring_service.get_system_status()
                    system_stats = system_status.get('system_stats', {})
                    if 'disk' in system_stats:
                        disk_info = system_stats['disk']
                        info['storage'] = {
                            'total': disk_info.get('total', 0),
                            'used': disk_info.get('used', 0),
                            'free': disk_info.get('free', 0),
                            'percent': disk_info.get('percent', 0)
                        }

                return jsonify({
                    'success': True,
                    'data': info
                })

            except Exception as e:
                self.logger.error(f"获取系统信息失败: {e}")
                return jsonify({'error': '获取系统信息失败'}), 500

        @self.app.route('/api/system/online-users', methods=['GET'])
        def get_online_users():
            """获取在线用户列表"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                # 获取在线用户
                user_service = self.services.get('user')
                if user_service:
                    online_users = user_service.get_online_users()
                    return jsonify({
                        'success': True,
                        'data': {
                            'users': online_users,
                            'count': len(online_users)
                        }
                    })
                else:
                    return jsonify({
                        'success': True,
                        'data': {
                            'users': [],
                            'count': 0
                        }
                    })

            except Exception as e:
                self.logger.error(f"获取在线用户失败: {e}")
                return jsonify({'error': '获取在线用户失败'}), 500

        @self.app.route('/api/admin/config', methods=['GET'])
        def get_system_config():
            """获取系统配置（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not self._check_permission(user_info, 'admin'):
                    return jsonify({'error': '需要管理员权限'}), 403

                # 获取完整配置
                config = self.settings.settings.copy()

                return jsonify({
                    'success': True,
                    'data': config
                })

            except Exception as e:
                self.logger.error(f"获取系统配置失败: {e}")
                return jsonify({'error': '获取配置失败'}), 500

        @self.app.route('/api/admin/config', methods=['PUT'])
        def update_system_config():
            """更新系统配置（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not self._check_permission(user_info, 'admin'):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()
                if not data:
                    return jsonify({'error': '配置数据不能为空'}), 400

                # 记录配置变更
                old_config = self.settings.settings.copy()

                # 更新配置
                for key, value in data.items():
                    self.settings.set(key, value)

                # 保存配置
                self.settings.save_settings()

                # 检查是否需要重启服务
                restart_required = self._check_restart_required(old_config, data)

                # 广播配置变更通知
                self.broadcast_notification(
                    f"系统配置已更新 - 用户: {user_info.get('username', 'unknown')}",
                    "config_update"
                )

                # 通过WebSocket推送配置变更
                self.socketio.emit('config_updated', {
                    'config': self.settings.settings,
                    'restart_required': restart_required,
                    'timestamp': datetime.now().isoformat()
                })

                return jsonify({
                    'success': True,
                    'message': '配置更新成功',
                    'restart_required': restart_required
                })

            except Exception as e:
                self.logger.error(f"更新系统配置失败: {e}")
                return jsonify({'error': '更新配置失败'}), 500

        @self.app.route('/api/config/public', methods=['GET'])
        def get_public_config():
            """获取公开配置（前端使用）"""
            try:
                # 只返回前端需要的配置信息
                public_config = {
                    'server': {
                        'host': self.settings.get('server.host', '0.0.0.0'),
                        'port': self.settings.get('server.port', 8086),
                        'frontend_port': self.settings.get('server.frontend_port', 8084)
                    },
                    'file_share': {
                        'allowed_extensions': self.settings.get('file_share.allowed_extensions', []),
                        'max_file_size': self.settings.get('file_share.max_file_size', 1073741824),
                        'thumbnail_sizes': self.settings.get('file_share.thumbnail_sizes', {})
                    },
                    'download': {
                        'enable_single_download': self.settings.get('download.enable_single_download', True),
                        'enable_batch_download': self.settings.get('download.enable_batch_download', True),
                        'enable_folder_download': self.settings.get('download.enable_folder_download', True),
                        'max_batch_files': self.settings.get('download.max_batch_files', 100),
                        'max_package_size': self.settings.get('download.max_package_size', 524288000)
                    },
                    'search': {
                        'enable_text_search': self.settings.get('search.enable_text_search', True),
                        'enable_image_search': self.settings.get('search.enable_image_search', True),
                        'max_search_results': self.settings.get('search.max_search_results', 1000)
                    },
                    'notifications': {
                        'enable_rolling_notifications': self.settings.get('notifications.enable_rolling_notifications', True),
                        'notification_duration': self.settings.get('notifications.notification_duration', 5000),
                        'max_notifications': self.settings.get('notifications.max_notifications', 10)
                    }
                }

                return jsonify({
                    'success': True,
                    'data': public_config
                })

            except Exception as e:
                self.logger.error(f"获取公开配置失败: {e}")
                return jsonify({'error': '获取配置失败'}), 500
    
    def register_socketio_events(self):
        """注册SocketIO事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            self.logger.info(f"客户端连接: {request.sid}")
            emit('connected', {'message': '连接成功'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接"""
            self.logger.info(f"客户端断开连接: {request.sid}")
        
        @self.socketio.on('ping')
        def handle_ping():
            """心跳检测"""
            emit('pong', {'timestamp': datetime.now().isoformat()})
    
    def broadcast_notification(self, message: str, notification_type: str = "info"):
        """广播通知"""
        try:
            self.socketio.emit('notification', {
                'message': message,
                'type': notification_type,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            self.logger.error(f"广播通知失败: {e}")
    
    def broadcast_user_activity(self, activity_data: Dict[str, Any]):
        """广播用户活动"""
        try:
            if self.socketio:
                self.socketio.emit('user_activity', activity_data)
        except Exception as e:
            self.logger.error(f"广播用户活动失败: {e}")

    def _validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证用户token"""
        try:
            if not token:
                return None

            user_service = self.services.get('user')
            if user_service:
                return user_service.validate_session(token)
            else:
                # 用户服务不可用时，返回临时用户信息
                return {
                    'user_id': 1,
                    'username': 'temp_user',
                    'permissions': ['read', 'download'],
                    'is_admin': True  # 临时给予管理员权限用于测试
                }

        except Exception as e:
            self.logger.error(f"验证token失败: {e}")
            return None

    def _check_permission(self, user_info: Dict[str, Any], permission: str) -> bool:
        """检查用户权限"""
        try:
            if not user_info:
                return False

            permissions = user_info.get('permissions', [])
            return permission in permissions or user_info.get('is_admin', False)

        except Exception as e:
            self.logger.error(f"检查权限失败: {e}")
            return False

    def _check_restart_required(self, old_config: Dict[str, Any], new_config: Dict[str, Any]) -> bool:
        """检查配置变更是否需要重启服务"""
        try:
            # 需要重启的配置项
            restart_keys = [
                'server.host',
                'server.port',
                'server.frontend_port',
                'database.host',
                'database.port',
                'database.username',
                'database.password',
                'database.database'
            ]

            for key in restart_keys:
                old_value = self._get_nested_value(old_config, key)
                new_value = self._get_nested_value(new_config, key)
                if old_value != new_value:
                    return True

            return False

        except Exception as e:
            self.logger.error(f"检查重启需求失败: {e}")
            return False

    def _get_nested_value(self, config: Dict[str, Any], key: str) -> Any:
        """获取嵌套配置值"""
        try:
            keys = key.split('.')
            value = config
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return None
            return value
        except:
            return None
    
    def run(self):
        """运行服务器"""
        try:
            self.running = True
            self.start_time = time.time()
            
            host = self.settings.get('server.host', '0.0.0.0')
            port = self.settings.get('server.port', 8080)
            debug = self.settings.get('server.debug', False)
            
            self.logger.info(f"API服务器启动: {host}:{port}")
            
            self.socketio.run(
                self.app,
                host=host,
                port=port,
                debug=debug,
                use_reloader=False
            )
            
        except Exception as e:
            self.logger.error(f"API服务器运行失败: {e}")
            self.running = False
    
    def stop(self):
        """停止服务器"""
        try:
            self.running = False
            self.logger.info("API服务器已停止")
        except Exception as e:
            self.logger.error(f"停止API服务器失败: {e}")
    
    def get_app(self):
        """获取Flask应用实例"""
        return self.app
    
    def get_socketio(self):
        """获取SocketIO实例"""
        return self.socketio
